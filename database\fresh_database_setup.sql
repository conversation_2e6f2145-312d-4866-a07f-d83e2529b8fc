-- FRESH DATABASE SETUP FOR UNIFIED AUTHENTICATION SYSTEM
-- This script creates a clean database schema designed for the new system
-- Run this script in Supabase SQL Editor

-- Step 1: Drop all existing tables and policies (CLEAN SLATE)
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON storage.objects;
DROP POLICY IF EXISTS "Enable read access for all users" ON storage.objects;
DROP POLICY IF EXISTS "Enable update for users based on email" ON storage.objects;
DROP POLICY IF EXISTS "Enable delete for users based on email" ON storage.objects;

-- Drop all existing policies on public tables
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Drop existing triggers and functions
DROP TRIGGER IF EXISTS auto_add_teacher_trigger ON public.classes;
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.auto_add_teacher_to_class();
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.is_class_member(UUID, UUID);
DROP FUNCTION IF EXISTS public.is_class_teacher(UUID, UUID);

-- Drop existing tables (in correct order to handle foreign keys)
DROP TABLE IF EXISTS public.notifications CASCADE;
DROP TABLE IF EXISTS public.tickets CASCADE;
DROP TABLE IF EXISTS public.submissions CASCADE;
DROP TABLE IF EXISTS public.assignments CASCADE;
DROP TABLE IF EXISTS public.class_members CASCADE;
DROP TABLE IF EXISTS public.classes CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Step 2: Create fresh tables with clean structure

-- Users table (NO ROLE COLUMN - unified authentication)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Classes table
CREATE TABLE public.classes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    subject TEXT,
    description TEXT,
    class_code TEXT UNIQUE NOT NULL,
    color_scheme TEXT DEFAULT 'blue',
    teacher_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Class members table (includes role for class-specific permissions)
CREATE TABLE public.class_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    class_id UUID REFERENCES public.classes(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('teacher', 'student')) DEFAULT 'student',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(class_id, user_id)
);

-- Assignments table
CREATE TABLE public.assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    class_id UUID REFERENCES public.classes(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    max_marks INTEGER NOT NULL DEFAULT 100,
    due_date TIMESTAMP WITH TIME ZONE,
    teacher_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    ai_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Submissions table
CREATE TABLE public.submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    assignment_id UUID REFERENCES public.assignments(id) ON DELETE CASCADE NOT NULL,
    student_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    file_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    ocr_text TEXT,
    grade INTEGER,
    feedback TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by TEXT,
    UNIQUE(assignment_id, student_id)
);

-- Tickets table (grade disputes)
CREATE TABLE public.tickets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID REFERENCES public.submissions(id) ON DELETE CASCADE NOT NULL,
    student_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    reason TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('open', 'resolved', 'closed')) DEFAULT 'open',
    teacher_response TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info',
    related_id UUID,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create indexes for performance
CREATE INDEX idx_classes_teacher_id ON public.classes(teacher_id);
CREATE INDEX idx_class_members_class_id ON public.class_members(class_id);
CREATE INDEX idx_class_members_user_id ON public.class_members(user_id);
CREATE INDEX idx_assignments_class_id ON public.assignments(class_id);
CREATE INDEX idx_assignments_teacher_id ON public.assignments(teacher_id);
CREATE INDEX idx_submissions_assignment_id ON public.submissions(assignment_id);
CREATE INDEX idx_submissions_student_id ON public.submissions(student_id);
CREATE INDEX idx_tickets_submission_id ON public.tickets(submission_id);
CREATE INDEX idx_tickets_student_id ON public.tickets(student_id);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);

-- Step 4: Create simple, non-recursive RLS policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users table policies (users can only access their own data)
CREATE POLICY "users_own_data" ON public.users
    FOR ALL USING (auth.uid() = id);

-- Classes table policies
CREATE POLICY "classes_teacher_access" ON public.classes
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "classes_member_read" ON public.classes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.class_members 
            WHERE class_id = id AND user_id = auth.uid()
        )
    );

-- Class members table policies
CREATE POLICY "class_members_teacher_access" ON public.class_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.classes 
            WHERE id = class_id AND teacher_id = auth.uid()
        )
    );

CREATE POLICY "class_members_own_membership" ON public.class_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "class_members_join" ON public.class_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Assignments table policies
CREATE POLICY "assignments_teacher_access" ON public.assignments
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "assignments_student_read" ON public.assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.class_members 
            WHERE class_id = assignments.class_id AND user_id = auth.uid()
        )
    );

-- Submissions table policies
CREATE POLICY "submissions_student_own" ON public.submissions
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "submissions_teacher_read" ON public.submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.assignments 
            WHERE id = assignment_id AND teacher_id = auth.uid()
        )
    );

CREATE POLICY "submissions_teacher_update" ON public.submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.assignments 
            WHERE id = assignment_id AND teacher_id = auth.uid()
        )
    );

-- Tickets table policies
CREATE POLICY "tickets_student_own" ON public.tickets
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "tickets_teacher_access" ON public.tickets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.submissions s
            JOIN public.assignments a ON s.assignment_id = a.id
            WHERE s.id = submission_id AND a.teacher_id = auth.uid()
        )
    );

-- Notifications table policies
CREATE POLICY "notifications_own" ON public.notifications
    FOR ALL USING (user_id = auth.uid());

-- Step 5: Create essential functions

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to auto-add teacher to their class
CREATE OR REPLACE FUNCTION public.add_teacher_to_class()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.class_members (class_id, user_id, role)
    VALUES (NEW.id, NEW.teacher_id, 'teacher');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create triggers
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

CREATE TRIGGER add_teacher_to_class_trigger
    AFTER INSERT ON public.classes
    FOR EACH ROW EXECUTE FUNCTION public.add_teacher_to_class();

-- Step 7: Success message
SELECT 'Fresh database setup completed successfully!' as status,
       'All tables recreated with clean structure' as change_1,
       'Simple RLS policies without recursion' as change_2,
       'Unified authentication system ready' as change_3,
       'Automatic OCR and grading compatible' as change_4;
