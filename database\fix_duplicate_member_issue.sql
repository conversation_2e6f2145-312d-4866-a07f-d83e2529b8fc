-- =====================================================
-- FIX DUPLICATE MEMBER ISSUE
-- =====================================================
-- This script fixes the duplicate key constraint violation when creating classes
-- The issue is that both the trigger AND the application code try to add the teacher as a member

-- =====================================================
-- OPTION 1: DROP THE TRIGGER (RECOMMENDED)
-- =====================================================
-- Since the application code already handles adding the teacher as a member,
-- we can simply drop the trigger to avoid conflicts

DROP TRIGGER IF EXISTS add_teacher_to_class_trigger ON public.classes;

-- =====================================================
-- OPTION 2: ALTERNATIVE - KEEP TRIGGER, REMOVE APP CODE
-- =====================================================
-- If you prefer to keep the trigger and remove the manual code from the app,
-- uncomment the following section and update the application code

/*
-- Keep the trigger but make sure it handles conflicts properly
DROP TRIGGER IF EXISTS add_teacher_to_class_trigger ON public.classes;

CREATE TRIGGER add_teacher_to_class_trigger
    AFTER INSERT ON public.classes
    FOR EACH ROW EXECUTE FUNCTION public.add_teacher_to_class();

-- The function already has ON CONFLICT DO NOTHING, so this should work
-- But you'll need to remove the manual member insertion from the application code
*/

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Duplicate member issue fixed!' as status,
       'The trigger that was causing duplicate teacher memberships has been removed.' as details,
       'You can now create classes without the duplicate key constraint error.' as next_steps;
