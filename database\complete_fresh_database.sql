-- COMPLETE FRESH DATABASE SETUP - CLEAN START
-- This script creates a fresh database for unified authentication
-- Users can be teachers (for classes they create) AND students (for classes they join)
-- Run this script in Supabase SQL Editor after manually clearing all tables

-- Step 1: Create fresh tables designed for unified authentication

-- Users table (NO GLOBAL ROLE - unified authentication)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Classes table
CREATE TABLE public.classes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    subject TEXT,
    description TEXT,
    class_code TEXT UNIQUE NOT NULL,
    color_scheme TEXT DEFAULT 'blue',
    teacher_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Class members table (handles both teachers and students)
-- A user can be a teacher in classes they created AND a student in classes they joined
CREATE TABLE public.class_members (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    class_id UUID REFERENCES public.classes(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('teacher', 'student')) DEFAULT 'student',
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(class_id, user_id)
);

-- Assignments table
CREATE TABLE public.assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    class_id UUID REFERENCES public.classes(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    max_marks INTEGER NOT NULL DEFAULT 100,
    due_date TIMESTAMP WITH TIME ZONE,
    teacher_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    ai_prompt TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Submissions table (automatic OCR and grading ready)
CREATE TABLE public.submissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    assignment_id UUID REFERENCES public.assignments(id) ON DELETE CASCADE NOT NULL,
    student_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    file_url TEXT NOT NULL,
    file_name TEXT NOT NULL,
    ocr_text TEXT,
    grade INTEGER,
    feedback TEXT,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by TEXT DEFAULT 'system',
    UNIQUE(assignment_id, student_id)
);

-- Tickets table (grade disputes)
CREATE TABLE public.tickets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    submission_id UUID REFERENCES public.submissions(id) ON DELETE CASCADE NOT NULL,
    student_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    reason TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('open', 'resolved', 'closed')) DEFAULT 'open',
    teacher_response TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info',
    related_id UUID,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create performance indexes
CREATE INDEX idx_classes_teacher_id ON public.classes(teacher_id);
CREATE INDEX idx_classes_class_code ON public.classes(class_code);
CREATE INDEX idx_class_members_class_id ON public.class_members(class_id);
CREATE INDEX idx_class_members_user_id ON public.class_members(user_id);
CREATE INDEX idx_class_members_role ON public.class_members(role);
CREATE INDEX idx_assignments_class_id ON public.assignments(class_id);
CREATE INDEX idx_assignments_teacher_id ON public.assignments(teacher_id);
CREATE INDEX idx_submissions_assignment_id ON public.submissions(assignment_id);
CREATE INDEX idx_submissions_student_id ON public.submissions(student_id);
CREATE INDEX idx_tickets_submission_id ON public.tickets(submission_id);
CREATE INDEX idx_tickets_student_id ON public.tickets(student_id);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_read ON public.notifications(read);

-- Step 4: Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Step 5: Create SIMPLE, NON-RECURSIVE RLS policies

-- Users: Users can only access their own data
CREATE POLICY "users_own_data" ON public.users
    FOR ALL USING (auth.uid() = id);

-- Classes: Teachers can manage their classes, members can read
CREATE POLICY "classes_teacher_full_access" ON public.classes
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "classes_member_read_access" ON public.classes
    FOR SELECT USING (
        id IN (
            SELECT class_id FROM public.class_members 
            WHERE user_id = auth.uid()
        )
    );

-- Class Members: Teachers can manage their class members, users can see their own memberships
CREATE POLICY "class_members_teacher_manage" ON public.class_members
    FOR ALL USING (
        class_id IN (
            SELECT id FROM public.classes 
            WHERE teacher_id = auth.uid()
        )
    );

CREATE POLICY "class_members_own_membership" ON public.class_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "class_members_join_classes" ON public.class_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Assignments: Teachers can manage, students can read
CREATE POLICY "assignments_teacher_manage" ON public.assignments
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "assignments_student_read" ON public.assignments
    FOR SELECT USING (
        class_id IN (
            SELECT class_id FROM public.class_members 
            WHERE user_id = auth.uid()
        )
    );

-- Submissions: Students manage their own, teachers can read/update for grading
CREATE POLICY "submissions_student_own" ON public.submissions
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "submissions_teacher_read_grade" ON public.submissions
    FOR SELECT USING (
        assignment_id IN (
            SELECT id FROM public.assignments 
            WHERE teacher_id = auth.uid()
        )
    );

CREATE POLICY "submissions_teacher_update_grade" ON public.submissions
    FOR UPDATE USING (
        assignment_id IN (
            SELECT id FROM public.assignments 
            WHERE teacher_id = auth.uid()
        )
    );

-- Tickets: Students manage their own, teachers can access for their assignments
CREATE POLICY "tickets_student_own" ON public.tickets
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "tickets_teacher_access" ON public.tickets
    FOR ALL USING (
        submission_id IN (
            SELECT s.id FROM public.submissions s
            INNER JOIN public.assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = auth.uid()
        )
    );

-- Notifications: Users can only access their own notifications
CREATE POLICY "notifications_own_access" ON public.notifications
    FOR ALL USING (user_id = auth.uid());

-- Step 6: Create essential functions for unified authentication

-- Function to handle new user registration (no role needed)
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically add teacher to their class as a member
CREATE OR REPLACE FUNCTION public.add_teacher_to_class()
RETURNS TRIGGER AS $$
BEGIN
    -- Add the teacher as a member of their own class with 'teacher' role
    INSERT INTO public.class_members (class_id, user_id, role)
    VALUES (NEW.id, NEW.teacher_id, 'teacher')
    ON CONFLICT (class_id, user_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create notifications
CREATE OR REPLACE FUNCTION public.create_notification(
    target_user_id UUID,
    notification_title TEXT,
    notification_message TEXT,
    notification_type TEXT DEFAULT 'info',
    related_entity_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, related_id)
    VALUES (target_user_id, notification_title, notification_message, notification_type, related_entity_id)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Create triggers for automatic functionality

-- Trigger to create user profile when auth user is created
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to add teacher to their class when class is created
CREATE TRIGGER add_teacher_to_class_trigger
    AFTER INSERT ON public.classes
    FOR EACH ROW EXECUTE FUNCTION public.add_teacher_to_class();

-- Step 8: Insert some test data for verification (optional)
-- You can remove this section if you don't want test data

-- Test user (will be created when someone signs up)
-- Test class and membership will be created when someone creates a class

-- Step 9: Success confirmation
SELECT
    'Clean database setup completed successfully!' as status,
    'Unified authentication system ready' as auth_system,
    'Users can be teachers AND students' as flexibility,
    'Automatic OCR and grading ready' as automation,
    'No table conflicts or errors' as stability,
    'Backend API compatible' as integration;
