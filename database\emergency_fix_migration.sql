-- EMERGENCY FIX: Resolve Infinite Recursion and Missing Columns
-- Run this script IMMEDIATELY in Supabase SQL Editor

-- Step 1: Drop ALL existing policies to prevent infinite recursion
DO $$ 
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Step 2: Add missing role column to class_members table (needed for existing functionality)
ALTER TABLE public.class_members ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'student';

-- Step 3: Update existing class_members records to have proper roles
-- Set teachers as 'teacher' role in their own classes
UPDATE public.class_members 
SET role = 'teacher' 
WHERE user_id IN (
  SELECT teacher_id 
  FROM public.classes 
  WHERE classes.id = class_members.class_id
);

-- Set all others as 'student' role
UPDATE public.class_members 
SET role = 'student' 
WHERE role IS NULL OR role = '';

-- Step 4: Create SIMPLE, NON-CIRCULAR RLS policies

-- Users table policies
CREATE POLICY "users_all_access" ON public.users
  FOR ALL USING (auth.uid() = id);

-- Classes table policies (SIMPLE - no circular references)
CREATE POLICY "classes_teacher_full_access" ON public.classes
  FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "classes_member_read_only" ON public.classes
  FOR SELECT USING (
    id IN (SELECT class_id FROM public.class_members WHERE user_id = auth.uid())
  );

-- Class members table policies (SIMPLE)
CREATE POLICY "class_members_own_records" ON public.class_members
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "class_members_teacher_access" ON public.class_members
  FOR ALL USING (
    class_id IN (SELECT id FROM public.classes WHERE teacher_id = auth.uid())
  );

-- Assignments table policies (SIMPLE)
CREATE POLICY "assignments_teacher_full_access" ON public.assignments
  FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "assignments_student_read_only" ON public.assignments
  FOR SELECT USING (
    class_id IN (SELECT class_id FROM public.class_members WHERE user_id = auth.uid())
  );

-- Submissions table policies (SIMPLE)
CREATE POLICY "submissions_student_own" ON public.submissions
  FOR ALL USING (student_id = auth.uid());

CREATE POLICY "submissions_teacher_read" ON public.submissions
  FOR SELECT USING (
    assignment_id IN (SELECT id FROM public.assignments WHERE teacher_id = auth.uid())
  );

CREATE POLICY "submissions_teacher_update" ON public.submissions
  FOR UPDATE USING (
    assignment_id IN (SELECT id FROM public.assignments WHERE teacher_id = auth.uid())
  );

-- Tickets table policies (SIMPLE)
CREATE POLICY "tickets_student_own" ON public.tickets
  FOR ALL USING (student_id = auth.uid());

CREATE POLICY "tickets_teacher_access" ON public.tickets
  FOR ALL USING (
    submission_id IN (
      SELECT s.id FROM public.submissions s
      JOIN public.assignments a ON s.assignment_id = a.id
      WHERE a.teacher_id = auth.uid()
    )
  );

-- Notifications table policies (SIMPLE)
CREATE POLICY "notifications_own_access" ON public.notifications
  FOR ALL USING (user_id = auth.uid());

-- Step 5: Create helper function for class membership
CREATE OR REPLACE FUNCTION public.is_class_member(user_uuid UUID, class_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.class_members 
    WHERE user_id = user_uuid AND class_id = class_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Create helper function for class teacher check
CREATE OR REPLACE FUNCTION public.is_class_teacher(user_uuid UUID, class_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.classes 
    WHERE id = class_uuid AND teacher_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 7: Update the join class function to automatically add teacher to class_members
CREATE OR REPLACE FUNCTION public.auto_add_teacher_to_class()
RETURNS TRIGGER AS $$
BEGIN
  -- Automatically add the teacher as a member of their own class
  INSERT INTO public.class_members (class_id, user_id, role)
  VALUES (NEW.id, NEW.teacher_id, 'teacher')
  ON CONFLICT (class_id, user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-add teacher to class_members
DROP TRIGGER IF EXISTS auto_add_teacher_trigger ON public.classes;
CREATE TRIGGER auto_add_teacher_trigger
  AFTER INSERT ON public.classes
  FOR EACH ROW EXECUTE FUNCTION public.auto_add_teacher_to_class();

-- Step 8: Ensure all tables have RLS enabled
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Step 9: Success message
SELECT 'Emergency fix applied successfully!' as status,
       'Infinite recursion resolved' as fix_1,
       'Class members role column restored' as fix_2,
       'Simple RLS policies created' as fix_3,
       'Helper functions added' as fix_4;
