import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FileTextIcon, LoaderIcon, CheckCircleIcon } from 'lucide-react';
import { extractText, gradeSubmission } from '../utils/api';
import { getAssignmentDetails, createSubmission, getSubmission, updateSubmission, createNotification } from '../utils/supabase';
import { useAuth } from '../context/AuthContext';
import { CloudinaryUploadResult } from '../utils/cloudinary';
import FileUpload from '../components/FileUpload';
import BackButton from '../components/BackButton';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorAlert from '../components/ErrorAlert';
import { toast } from 'react-toastify';
const SubmitAssignment = () => {
  const {
    classId,
    assignmentId
  } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [assignment, setAssignment] = useState<any>(null);
  const [existingSubmission, setExistingSubmission] = useState<any>(null);
  const [uploadedFile, setUploadedFile] = useState<CloudinaryUploadResult | null>(null);
  const [isProcessingOcr, setIsProcessingOcr] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [extractedText, setExtractedText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (!assignmentId) {
          throw new Error('Assignment ID is required');
        }

        if (!user) {
          throw new Error('User not authenticated');
        }

        const { data: assignment, error } = await getAssignmentDetails(assignmentId);

        if (error) {
          throw error;
        }

        if (!assignment) {
          throw new Error('Assignment not found');
        }

        setAssignment(assignment);

        // Check if user already has a submission for this assignment
        const { data: submissionData, error: submissionError } = await getSubmission(assignmentId, user.id);

        if (submissionData) {
          setExistingSubmission(submissionData);
          setExtractedText(submissionData.ocr_text || '');
        }
        // If no submission exists, that's fine - user can create one

      } catch (err: any) {
        setError(err.message || 'Failed to load assignment details');
        console.error('Error fetching data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [assignmentId, classId, user]);
  const handleFileUpload = async (result: CloudinaryUploadResult) => {
    setUploadedFile(result);
    setError(null);
    toast.success('File uploaded successfully! Processing text...');

    // Automatically trigger OCR processing
    await handleProcessOcr(result);
  };

  const handleUploadError = (error: string) => {
    setError(error);
    toast.error(error);
  };

  const handleProcessOcr = async (fileResult?: CloudinaryUploadResult) => {
    const fileToProcess = fileResult || uploadedFile;

    if (!fileToProcess) {
      setError('Please upload a file first');
      return;
    }

    setIsProcessingOcr(true);
    setError(null);

    try {
      // Create a temporary file object for OCR processing
      // We need to fetch the file from Cloudinary URL and convert it to a File object
      const response = await fetch(fileToProcess.secure_url);
      const blob = await response.blob();
      const file = new File([blob], fileToProcess.original_filename, { type: blob.type });

      const result = await extractText(file);
      console.log('OCR result:', result);

      if (result.data?.extracted_text) {
        setExtractedText(result.data.extracted_text);
        console.log('Extracted text:', result.data.extracted_text.substring(0, 100) + '...');
        toast.success('Text extracted successfully!');

        // Automatically submit the assignment after OCR completion
        if (fileResult) { // Only auto-submit if this was triggered by file upload
          await handleAutoSubmit(fileToProcess, result.data.extracted_text);
        }
      } else {
        const errorMessage = result.error || result.data?.error || 'Failed to extract text from the file';
        console.error('OCR failed:', errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Error processing OCR. Please try again.';
      if (err.response?.data?.supported_types) {
        setError(`${errorMessage}. Supported file types: ${err.response.data.supported_types.join(', ')}`);
      } else {
        setError(errorMessage);
      }
      console.error('Error processing OCR:', err.response?.data || err);
      toast.error(errorMessage);
    } finally {
      setIsProcessingOcr(false);
    }
  };

  const handleAutoSubmit = async (fileResult: CloudinaryUploadResult, ocrText: string) => {
    if (!user || !assignment) {
      console.error('Missing user or assignment data for auto-submit');
      return;
    }

    setIsSubmitting(true);

    try {
      toast.info('Submitting assignment and processing grade...');

      // Create or update submission in database
      const submissionData = {
        assignment_id: assignmentId!,
        student_id: user.id,
        file_url: fileResult.secure_url,
        file_name: fileResult.original_filename,
        ocr_text: ocrText
      };

      let submissionResult;
      if (existingSubmission) {
        // Update existing submission
        submissionResult = await updateSubmission(existingSubmission.id, {
          file_url: fileResult.secure_url,
          file_name: fileResult.original_filename,
          ocr_text: ocrText
        });
      } else {
        // Create new submission
        submissionResult = await createSubmission(submissionData);
      }

      if (submissionResult.error) {
        throw new Error(submissionResult.error.message || 'Failed to save submission');
      }

      // Grade the submission automatically
      const gradingResult = await gradeSubmission(
        'Compare OCR\'d content with Generated document',
        ocrText,
        assignment,
        undefined,
        undefined
      );

      if (gradingResult.success && submissionResult.data) {
        // Update submission with grade
        await updateSubmission(submissionResult.data.id, {
          grade: gradingResult.data.final_marks,
          feedback: gradingResult.data.review,
          graded_at: new Date().toISOString(),
          graded_by: 'system' // Automatic grading
        });

        // Create notification for student about grade
        await createNotification({
          user_id: user.id,
          title: 'Assignment Graded',
          message: `Your assignment "${assignment.title}" has been automatically graded. Score: ${gradingResult.data.final_marks}/${assignment.max_marks}`,
          type: 'grade',
          related_id: submissionResult.data.id
        });

        toast.success(`Assignment submitted and graded! Score: ${gradingResult.data.final_marks}/${assignment.max_marks}`);
      } else {
        toast.success('Assignment submitted successfully! Grading in progress...');
      }

      // Navigate back to assignment details
      setTimeout(() => {
        navigate(`/classes/${classId}/assignments/${assignmentId}`);
      }, 2000);

    } catch (err: any) {
      setError(err.message || 'Failed to submit assignment. Please try again.');
      console.error('Error in auto-submit:', err);
      toast.error(err.message || 'Failed to submit assignment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    if (!uploadedFile) {
      setError('Please upload a file before submitting');
      setIsSubmitting(false);
      return;
    }

    if (!extractedText) {
      setError('Please process OCR before submitting');
      setIsSubmitting(false);
      return;
    }

    if (!user) {
      setError('User not authenticated');
      setIsSubmitting(false);
      return;
    }

    try {
      // Create or update submission in database
      const submissionData = {
        assignment_id: assignmentId!,
        student_id: user.id,
        file_url: uploadedFile.secure_url,
        file_name: uploadedFile.original_filename,
        ocr_text: extractedText
      };

      let submissionResult;
      if (existingSubmission) {
        // Update existing submission
        submissionResult = await updateSubmission(existingSubmission.id, {
          file_url: uploadedFile.secure_url,
          file_name: uploadedFile.original_filename,
          ocr_text: extractedText
        });
      } else {
        // Create new submission
        submissionResult = await createSubmission(submissionData);
      }

      if (submissionResult.error) {
        throw new Error(submissionResult.error.message || 'Failed to save submission');
      }

      // Grade the submission automatically
      const gradingResult = await gradeSubmission(
        'Compare OCR\'d content with Generated document',
        extractedText,
        assignment,
        undefined,
        undefined
      );

      if (gradingResult.success && submissionResult.data) {
        // Update submission with grade
        await updateSubmission(submissionResult.data.id, {
          grade: gradingResult.data.final_marks,
          feedback: gradingResult.data.review,
          graded_at: new Date().toISOString(),
          graded_by: 'system' // Automatic grading
        });

        // Create notification for student about grade
        await createNotification({
          user_id: user.id,
          title: 'Assignment Graded',
          message: `Your assignment "${assignment.title}" has been automatically graded. Score: ${gradingResult.data.final_marks}/${assignment.max_marks}`,
          type: 'grade',
          related_id: submissionResult.data.id
        });
      }

      toast.success('Assignment submitted successfully!');
      navigate(`/classes/${classId}/assignments/${assignmentId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to submit assignment. Please try again.');
      console.error('Error submitting assignment:', err);
      toast.error(err.message || 'Failed to submit assignment');
    } finally {
      setIsSubmitting(false);
    }
  };
  if (isLoading) {
    return <div className="h-64">
        <LoadingSpinner size="medium" />
      </div>;
  }
  
  if (error) {
    return <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <BackButton to={`/classes/${classId}/assignments/${assignmentId}`} />
        </div>
        <ErrorAlert 
          message={error} 
          onDismiss={() => setError(null)} 
        />
      </div>;
  }
  return <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <BackButton to={`/classes/${classId}/assignments/${assignmentId}`} />
      </div>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-2xl font-bold mb-2">Submit Assignment</h1>
            <h2 className="text-lg text-gray-700 mb-4">{assignment.title}</h2>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium mb-2">Assignment Content:</h3>
              <div className="whitespace-pre-wrap text-gray-700">
                {assignment.content}
              </div>
            </div>

            {existingSubmission && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                <h3 className="font-medium text-yellow-800 mb-2">
                  Existing Submission Found
                </h3>
                <p className="text-sm text-yellow-700 mb-2">
                  You have already submitted this assignment. You can upload a new file to update your submission.
                </p>
                <div className="text-sm text-yellow-600">
                  <p>Previous submission: {existingSubmission.file_name}</p>
                  <p>Submitted: {new Date(existingSubmission.submitted_at).toLocaleString()}</p>
                  {existingSubmission.grade && (
                    <p>Grade: {existingSubmission.grade}/{assignment.max_marks}</p>
                  )}
                </div>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="font-medium text-blue-800 mb-2">
                Automatic Assignment Submission
              </h3>
              <p className="text-sm text-blue-700 mb-4">
                Upload a document (PDF) or image (PNG, JPG, JPEG, AVIF) of your work.
                The system will automatically extract text, submit your assignment, and grade it for you!
              </p>

              <FileUpload
                onUploadComplete={handleFileUpload}
                onUploadError={handleUploadError}
                disabled={isSubmitting || isProcessingOcr}
                className="mb-4"
              />

              {isProcessingOcr && (
                <div className="mb-4 flex items-center text-blue-600">
                  <LoaderIcon className="h-5 w-5 mr-2 animate-spin" />
                  <span>Processing text extraction and submission...</span>
                </div>
              )}

              {extractedText && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-2 text-green-600">
                    <CheckCircleIcon className="h-5 w-5 mr-2" />
                    <h3 className="font-medium">OCR Extraction Complete</h3>
                  </div>
                  <div className="whitespace-pre-wrap text-gray-700 max-h-64 overflow-y-auto">
                    {extractedText}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {extractedText ? (
              <span className="text-green-600 flex items-center">
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                Assignment submitted and graded automatically!
              </span>
            ) : (
              <span>Upload a file to automatically submit and grade your assignment</span>
            )}
          </div>
          <button
            type="button"
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            onClick={() => navigate(`/classes/${classId}/assignments/${assignmentId}`)}
            disabled={isSubmitting || isProcessingOcr}
          >
            {isSubmitting || isProcessingOcr ? 'Processing...' : 'Back to Assignment'}
          </button>
        </div>
      </form>
    </div>;
};
export default SubmitAssignment;