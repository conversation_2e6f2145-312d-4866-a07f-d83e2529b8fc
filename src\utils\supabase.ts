import { createClient } from '@supabase/supabase-js';

// Supabase configuration from environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://nrvautzqoohgwqjyjheg.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5ydmF1dHpxb29oZ3dxanlqaGVnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMjE5OTMsImV4cCI6MjA2NjU5Nzk5M30.H-W9AjdGhWFoxVSEHqqI4elf5SnP4q80qSqlXQwHC0Y';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'teacher' | 'student';
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Class {
  id: string;
  name: string;
  subject: string;
  description: string;
  class_code: string;
  color_scheme: string;
  teacher_id: string;
  created_at: string;
  updated_at: string;
}

export interface ClassMember {
  id: string;
  class_id: string;
  user_id: string;
  role: 'teacher' | 'student';
  joined_at: string;
}

export interface Assignment {
  id: string;
  class_id: string;
  teacher_id: string;
  title: string;
  description: string;
  content: string;
  max_marks: number;
  due_date: string;
  is_ai_generated: boolean;
  ai_prompt?: string;
  created_at: string;
  updated_at: string;
}

export interface Submission {
  id: string;
  assignment_id: string;
  student_id: string;
  file_url: string;
  file_name: string;
  ocr_text: string;
  grade?: number;
  feedback?: string;
  graded_at?: string;
  graded_by?: string;
  submitted_at: string;
}

export interface Ticket {
  id: string;
  submission_id: string;
  student_id: string;
  title: string;
  description: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  response?: string;
  responded_by?: string;
  responded_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'grade' | 'assignment' | 'class' | 'ticket' | 'general';
  is_read: boolean;
  related_id?: string;
  created_at: string;
}

// Helper functions for authentication
export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  return user;
};

export const signUp = async (email: string, password: string, name: string) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name
        }
      }
    });

    if (error) {
      throw error;
    }

    // If signup successful, create user profile manually
    if (data.user) {
      try {
        // Wait a moment for the auth user to be fully created
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create user profile manually
        const { error: profileError } = await supabase
          .from('users')
          .upsert({
            id: data.user.id,
            email: email,
            name: name
          }, {
            onConflict: 'id'
          });

        if (profileError) {
          console.error('Profile creation error:', profileError);
          // Don't throw here, let the signup succeed
        } else {
          console.log('User profile created successfully');
        }
      } catch (profileError) {
        console.error('Could not create profile manually:', profileError);
        // Don't throw here, let the signup succeed
      }
    }

    return { data, error: null };
  } catch (error) {
    console.error('Signup error:', error);
    return { data: null, error };
  }
};

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  return { data, error };
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();

  // Clear any cached data to prevent stale state issues
  clearCachedData();

  return { error };
};

// Helper function to clear cached data and force a clean state
export const clearCachedData = () => {
  try {
    console.log('Clearing all cached data...');

    // Clear ALL localStorage (more aggressive approach)
    localStorage.clear();

    // Clear sessionStorage as well
    sessionStorage.clear();

    // Clear any IndexedDB data that Supabase might use
    if ('indexedDB' in window) {
      try {
        // Clear Supabase's IndexedDB
        indexedDB.deleteDatabase('supabase-js-db');
      } catch (idbError) {
        console.warn('Could not clear IndexedDB:', idbError);
      }
    }

    // Force reload the page to ensure clean state
    console.log('Cached data cleared successfully, reloading page...');
    setTimeout(() => {
      window.location.reload();
    }, 100);

  } catch (error) {
    console.error('Error clearing cached data:', error);
    // Force reload even if clearing fails
    window.location.reload();
  }
};

// Function to force a complete application reset
export const forceAppReset = () => {
  console.log('Forcing complete application reset...');
  clearCachedData();
};

// Helper function to get user profile
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
  return { data, error };
};

// Helper function to update user profile
export const updateUserProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  return { data, error };
};

// Helper function to upload avatar using Cloudinary
export const uploadAvatar = async (userId: string, file: File) => {
  try {
    // Import Cloudinary upload function
    const { uploadToCloudinary } = await import('./cloudinary');

    // Create a modified file with a specific name for avatars
    const fileExt = file.name.split('.').pop();
    const fileName = `avatar_${userId}.${fileExt}`;
    const renamedFile = new File([file], fileName, { type: file.type });

    // Upload to Cloudinary with avatars folder
    const result = await uploadToCloudinary(renamedFile, undefined, 'avatars');

    return {
      data: { publicUrl: result.secure_url },
      error: null
    };
  } catch (error) {
    console.error('Avatar upload error:', error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error('Failed to upload avatar')
    };
  }
};

// Class management functions
export const createClass = async (classData: {
  name: string;
  subject: string;
  description: string;
  color_scheme: string;
  teacher_id: string;
}) => {
  try {
    // Generate unique class code
    let classCode: string;
    let isUnique = false;
    let attempts = 0;

    do {
      // Generate a 6-character alphanumeric code
      classCode = Math.random().toString(36).substring(2, 8).toUpperCase();

      // Check if code is unique
      const { data: existingClass } = await supabase
        .from('classes')
        .select('id')
        .eq('class_code', classCode)
        .single();

      isUnique = !existingClass;
      attempts++;
    } while (!isUnique && attempts < 10);

    const { data, error } = await supabase
      .from('classes')
      .insert({
        ...classData,
        class_code: classCode
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating class:', error);
      return { data: null, error };
    }

    // Add teacher as class member
    const { error: memberError } = await supabase
      .from('class_members')
      .insert({
        class_id: data.id,
        user_id: classData.teacher_id,
        role: 'teacher'
      });

    if (memberError) {
      console.error('Error adding teacher as member:', memberError);
      // If adding member fails, delete the class
      await supabase.from('classes').delete().eq('id', data.id);
      return { data: null, error: memberError };
    }

    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error in createClass:', error);
    return { data: null, error };
  }
};

export const joinClass = async (userId: string, classCode: string) => {
  // First, find the class by code
  const { data: classData, error: classError } = await supabase
    .from('classes')
    .select('id')
    .eq('class_code', classCode)
    .single();

  if (classError || !classData) {
    return { data: null, error: classError || new Error('Class not found') };
  }

  // Check if user is already a member
  const { data: existingMember } = await supabase
    .from('class_members')
    .select('id')
    .eq('class_id', classData.id)
    .eq('user_id', userId)
    .single();

  if (existingMember) {
    return { data: null, error: new Error('You are already a member of this class') };
  }

  // Add user as student
  const { data, error } = await supabase
    .from('class_members')
    .insert({
      class_id: classData.id,
      user_id: userId,
      role: 'student'
    })
    .select()
    .single();

  return { data, error };
};

export const getUserClasses = async (userId: string) => {
  const { data, error } = await supabase
    .from('class_members')
    .select(`
      id,
      role,
      joined_at,
      classes (
        id,
        name,
        subject,
        description,
        class_code,
        color_scheme,
        teacher_id,
        created_at,
        users!classes_teacher_id_fkey (
          id,
          name,
          email
        )
      )
    `)
    .eq('user_id', userId);

  return { data, error };
};

export const getClassDetails = async (classId: string) => {
  const { data, error } = await supabase
    .from('classes')
    .select(`
      *,
      users!classes_teacher_id_fkey (
        id,
        name,
        email
      ),
      class_members (
        id,
        role,
        joined_at,
        users (
          id,
          name,
          email,
          avatar_url
        )
      )
    `)
    .eq('id', classId)
    .single();

  return { data, error };
};

export const leaveClass = async (userId: string, classId: string) => {
  const { error } = await supabase
    .from('class_members')
    .delete()
    .eq('user_id', userId)
    .eq('class_id', classId);

  return { error };
};

export const updateClass = async (classId: string, updates: Partial<Class>) => {
  const { data, error } = await supabase
    .from('classes')
    .update(updates)
    .eq('id', classId)
    .select()
    .single();

  return { data, error };
};

export const deleteClass = async (classId: string) => {
  const { error } = await supabase
    .from('classes')
    .delete()
    .eq('id', classId);

  return { error };
};

// Assignment management functions
export const createAssignment = async (assignmentData: {
  class_id: string;
  teacher_id: string;
  title: string;
  description: string;
  content: string;
  max_marks: number;
  due_date: string;
  is_ai_generated: boolean;
  ai_prompt?: string;
}) => {
  const { data, error } = await supabase
    .from('assignments')
    .insert(assignmentData)
    .select()
    .single();

  return { data, error };
};

export const getClassAssignments = async (classId: string) => {
  const { data, error } = await supabase
    .from('assignments')
    .select(`
      *,
      users!assignments_teacher_id_fkey (
        id,
        name,
        email
      )
    `)
    .eq('class_id', classId)
    .order('created_at', { ascending: false });

  return { data, error };
};

export const getAssignmentDetails = async (assignmentId: string) => {
  const { data, error } = await supabase
    .from('assignments')
    .select(`
      *,
      users!assignments_teacher_id_fkey (
        id,
        name,
        email
      ),
      classes (
        id,
        name,
        subject
      )
    `)
    .eq('id', assignmentId)
    .single();

  return { data, error };
};

export const updateAssignment = async (assignmentId: string, updates: Partial<Assignment>) => {
  const { data, error } = await supabase
    .from('assignments')
    .update(updates)
    .eq('id', assignmentId)
    .select()
    .single();

  return { data, error };
};

export const deleteAssignment = async (assignmentId: string) => {
  const { error } = await supabase
    .from('assignments')
    .delete()
    .eq('id', assignmentId);

  return { error };
};

export const getUserAssignments = async (userId: string) => {
  // First get the user's class IDs
  const { data: classMembers, error: memberError } = await supabase
    .from('class_members')
    .select('class_id')
    .eq('user_id', userId);

  if (memberError || !classMembers) {
    return { data: null, error: memberError };
  }

  const classIds = classMembers.map(member => member.class_id);

  if (classIds.length === 0) {
    return { data: [], error: null };
  }

  const { data, error } = await supabase
    .from('assignments')
    .select(`
      *,
      classes (
        id,
        name,
        subject,
        color_scheme
      ),
      users!assignments_teacher_id_fkey (
        id,
        name,
        email
      ),
      submissions!left (
        id,
        grade,
        submitted_at,
        graded_at
      )
    `)
    .in('class_id', classIds)
    .order('due_date', { ascending: true });

  return { data, error };
};

// Submission management functions
export const createSubmission = async (submissionData: {
  assignment_id: string;
  student_id: string;
  file_url: string;
  file_name: string;
  ocr_text?: string;
}) => {
  const { data, error } = await supabase
    .from('submissions')
    .insert(submissionData)
    .select()
    .single();

  return { data, error };
};

export const updateSubmission = async (submissionId: string, updates: {
  ocr_text?: string;
  grade?: number;
  feedback?: string;
  graded_at?: string;
  graded_by?: string;
}) => {
  const { data, error } = await supabase
    .from('submissions')
    .update(updates)
    .eq('id', submissionId)
    .select()
    .single();

  return { data, error };
};

export const getSubmission = async (assignmentId: string, studentId: string) => {
  const { data, error } = await supabase
    .from('submissions')
    .select(`
      *,
      assignments (
        id,
        title,
        content,
        max_marks,
        due_date
      ),
      users!submissions_student_id_fkey (
        id,
        name,
        email
      )
    `)
    .eq('assignment_id', assignmentId)
    .eq('student_id', studentId)
    .single();

  return { data, error };
};

export const getAssignmentSubmissions = async (assignmentId: string) => {
  const { data, error } = await supabase
    .from('submissions')
    .select(`
      *,
      users!submissions_student_id_fkey (
        id,
        name,
        email
      )
    `)
    .eq('assignment_id', assignmentId)
    .order('submitted_at', { ascending: false });

  return { data, error };
};

export const getUserSubmissions = async (userId: string) => {
  const { data, error } = await supabase
    .from('submissions')
    .select(`
      *,
      assignments (
        id,
        title,
        max_marks,
        due_date,
        classes (
          id,
          name,
          subject
        )
      )
    `)
    .eq('student_id', userId)
    .order('submitted_at', { ascending: false });

  return { data, error };
};

export const getSubmissionById = async (submissionId: string) => {
  const { data, error } = await supabase
    .from('submissions')
    .select(`
      *,
      assignments (
        id,
        title,
        content,
        max_marks,
        due_date,
        classes (
          id,
          name,
          subject
        )
      ),
      users!submissions_student_id_fkey (
        id,
        name,
        email
      )
    `)
    .eq('id', submissionId)
    .single();

  return { data, error };
};

// Ticket management functions
export const createTicket = async (ticketData: {
  submission_id: string;
  student_id: string;
  title: string;
  description: string;
}) => {
  const { data, error } = await supabase
    .from('tickets')
    .insert(ticketData)
    .select()
    .single();

  return { data, error };
};

export const updateTicket = async (ticketId: string, updates: {
  status?: 'open' | 'in_progress' | 'resolved' | 'closed';
  response?: string;
  responded_by?: string;
  responded_at?: string;
}) => {
  const { data, error } = await supabase
    .from('tickets')
    .update(updates)
    .eq('id', ticketId)
    .select()
    .single();

  return { data, error };
};

export const getTicketsBySubmission = async (submissionId: string) => {
  const { data, error } = await supabase
    .from('tickets')
    .select(`
      *,
      users!tickets_student_id_fkey (
        id,
        name,
        email
      ),
      users!tickets_responded_by_fkey (
        id,
        name,
        email
      ),
      submissions (
        id,
        assignments (
          id,
          title,
          classes (
            id,
            name
          )
        )
      )
    `)
    .eq('submission_id', submissionId)
    .order('created_at', { ascending: false });

  return { data, error };
};

export const getUserTickets = async (userId: string) => {
  const { data, error } = await supabase
    .from('tickets')
    .select(`
      *,
      submissions (
        id,
        assignments (
          id,
          title,
          max_marks,
          classes (
            id,
            name,
            subject
          )
        )
      ),
      users!tickets_responded_by_fkey (
        id,
        name,
        email
      )
    `)
    .eq('student_id', userId)
    .order('created_at', { ascending: false });

  return { data, error };
};

export const getClassTickets = async (classId: string) => {
  const { data, error } = await supabase
    .from('tickets')
    .select(`
      *,
      student:users!tickets_student_id_fkey (
        id,
        name,
        email
      ),
      responder:users!tickets_responded_by_fkey (
        id,
        name,
        email
      ),
      submissions (
        id,
        grade,
        assignments!inner (
          id,
          title,
          max_marks,
          class_id
        )
      )
    `)
    .eq('submissions.assignments.class_id', classId)
    .order('created_at', { ascending: false });

  return { data, error };
};

// New function to get all tickets for a teacher across all their classes
export const getTeacherTickets = async (teacherId: string) => {
  const { data, error } = await supabase
    .from('tickets')
    .select(`
      *,
      student:users!tickets_student_id_fkey (
        id,
        name,
        email
      ),
      responder:users!tickets_responded_by_fkey (
        id,
        name,
        email
      ),
      submissions (
        id,
        grade,
        assignments!inner (
          id,
          title,
          max_marks,
          class_id,
          classes!inner (
            id,
            name,
            teacher_id
          )
        )
      )
    `)
    .eq('submissions.assignments.classes.teacher_id', teacherId)
    .order('created_at', { ascending: false });

  return { data, error };
};

// Notification management functions
export const createNotification = async (notificationData: {
  user_id: string;
  title: string;
  message: string;
  type: 'grade' | 'assignment' | 'class' | 'ticket' | 'general';
  related_id?: string;
}) => {
  const { data, error } = await supabase
    .from('notifications')
    .insert(notificationData)
    .select()
    .single();

  return { data, error };
};

export const getUserNotifications = async (userId: string, limit: number = 50) => {
  const { data, error } = await supabase
    .from('notifications')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit);

  return { data, error };
};

export const markNotificationAsRead = async (notificationId: string) => {
  const { data, error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('id', notificationId)
    .select()
    .single();

  return { data, error };
};

export const markAllNotificationsAsRead = async (userId: string) => {
  const { data, error } = await supabase
    .from('notifications')
    .update({ is_read: true })
    .eq('user_id', userId)
    .eq('is_read', false);

  return { data, error };
};

export const getUnreadNotificationCount = async (userId: string) => {
  const { count, error } = await supabase
    .from('notifications')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('is_read', false);

  return { count, error };
};

export const deleteNotification = async (notificationId: string) => {
  const { error } = await supabase
    .from('notifications')
    .delete()
    .eq('id', notificationId);

  return { error };
};

export const getClassMembers = async (classId: string) => {
  const { data, error } = await supabase
    .from('class_members')
    .select(`
      id,
      role,
      joined_at,
      users (
        id,
        name,
        email
      )
    `)
    .eq('class_id', classId)
    .order('role', { ascending: false }) // Teachers first
    .order('users(name)', { ascending: true });

  return { data, error };
};

// Get total student count for a user (counts students in classes where user is teacher or member)
export const getTotalStudentCount = async (userId: string) => {
  try {
    // Get classes where user is teacher
    const { data: teacherClasses, error: classError } = await supabase
      .from('classes')
      .select('id')
      .eq('teacher_id', userId);

    // Get classes where user is a member
    const { data: memberClasses, error: memberError } = await supabase
      .from('class_members')
      .select('class_id')
      .eq('user_id', userId);

    if (classError && memberError) {
      return { count: 0, error: classError || memberError };
    }

    // Combine all class IDs
    const teacherClassIds = teacherClasses?.map(cls => cls.id) || [];
    const memberClassIds = memberClasses?.map(member => member.class_id) || [];
    const allClassIds = [...new Set([...teacherClassIds, ...memberClassIds])];

    if (allClassIds.length === 0) {
      return { count: 0, error: null };
    }

    // Count unique students across all their classes (excluding the user themselves)
    const { data, error } = await supabase
      .from('class_members')
      .select('user_id')
      .in('class_id', allClassIds)
      .neq('user_id', userId);

    if (error) {
      return { count: 0, error };
    }

    // Count unique students
    const uniqueStudents = new Set(data?.map(member => member.user_id) || []);
    return { count: uniqueStudents.size, error: null };
  } catch (error) {
    return { count: 0, error };
  }
};

// Class-based permission checking functions
export const isUserTeacherOfClass = async (userId: string, classId: string) => {
  try {
    const { data, error } = await supabase
      .from('classes')
      .select('teacher_id')
      .eq('id', classId)
      .eq('teacher_id', userId)
      .single();

    return { isTeacher: !error && !!data, error };
  } catch (error) {
    return { isTeacher: false, error };
  }
};

export const isUserStudentInClass = async (userId: string, classId: string) => {
  try {
    const { data, error } = await supabase
      .from('class_members')
      .select('id')
      .eq('class_id', classId)
      .eq('user_id', userId)
      .single();

    return { isStudent: !error && !!data, error };
  } catch (error) {
    return { isStudent: false, error };
  }
};

export const getUserClassRole = async (userId: string, classId: string) => {
  try {
    // Check if user is teacher of the class
    const { isTeacher } = await isUserTeacherOfClass(userId, classId);
    if (isTeacher) {
      return { role: 'teacher', error: null };
    }

    // Check if user is student in the class
    const { isStudent } = await isUserStudentInClass(userId, classId);
    if (isStudent) {
      return { role: 'student', error: null };
    }

    return { role: null, error: null };
  } catch (error) {
    return { role: null, error };
  }
};

export const getUserCreatedClasses = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('classes')
      .select('*')
      .eq('teacher_id', userId)
      .order('created_at', { ascending: false });

    return { data, error };
  } catch (error) {
    return { data: null, error };
  }
};

export const getUserJoinedClasses = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('class_members')
      .select(`
        class_id,
        joined_at,
        classes (
          id,
          name,
          subject,
          description,
          class_code,
          color_scheme,
          teacher_id,
          created_at,
          users!classes_teacher_id_fkey (
            id,
            name,
            email
          )
        )
      `)
      .eq('user_id', userId)
      .order('joined_at', { ascending: false });

    return { data, error };
  } catch (error) {
    return { data: null, error };
  }
};
