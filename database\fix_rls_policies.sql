-- =====================================================
-- FIX RLS POLICIES - REMOVE INFINITE RECURSION
-- =====================================================
-- Run this script in Supabase SQL Editor to fix the infinite recursion issues
-- This will drop all existing policies and recreate them with non-recursive logic

-- =====================================================
-- STEP 1: DROP ALL EXISTING POLICIES
-- =====================================================

-- Drop policies on classes table
DROP POLICY IF EXISTS "classes_teacher_full_access" ON public.classes;
DROP POLICY IF EXISTS "classes_member_read_access" ON public.classes;
DROP POLICY IF EXISTS "classes_teacher_manage" ON public.classes;
DROP POLICY IF EXISTS "classes_public_read" ON public.classes;

-- Drop policies on class_members table
DROP POLICY IF EXISTS "class_members_teacher_manage" ON public.class_members;
DROP POLICY IF EXISTS "class_members_own_membership" ON public.class_members;
DROP POLICY IF EXISTS "class_members_join_classes" ON public.class_members;

-- Drop policies on assignments table
DROP POLICY IF EXISTS "assignments_teacher_manage" ON public.assignments;
DROP POLICY IF EXISTS "assignments_student_read" ON public.assignments;

-- Drop policies on submissions table
DROP POLICY IF EXISTS "submissions_student_own" ON public.submissions;
DROP POLICY IF EXISTS "submissions_teacher_read_grade" ON public.submissions;
DROP POLICY IF EXISTS "submissions_teacher_update_grade" ON public.submissions;
DROP POLICY IF EXISTS "submissions_teacher_access" ON public.submissions;
DROP POLICY IF EXISTS "submissions_teacher_update" ON public.submissions;

-- Drop policies on tickets table
DROP POLICY IF EXISTS "tickets_student_own" ON public.tickets;
DROP POLICY IF EXISTS "tickets_teacher_access" ON public.tickets;

-- =====================================================
-- STEP 2: CREATE NON-RECURSIVE POLICIES
-- =====================================================

-- Classes: Simple policies to avoid recursion
-- Teachers can manage their own classes
CREATE POLICY "classes_teacher_manage" ON public.classes
    FOR ALL USING (teacher_id = auth.uid());

-- Anyone can read classes (we'll control access through class_members)
CREATE POLICY "classes_public_read" ON public.classes
    FOR SELECT USING (true);

-- Class Members: Direct policies without cross-references
-- Users can see their own memberships
CREATE POLICY "class_members_own_membership" ON public.class_members
    FOR SELECT USING (user_id = auth.uid());

-- Users can join classes (insert their own membership)
CREATE POLICY "class_members_join_classes" ON public.class_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Teachers can manage memberships for their classes (direct teacher_id check)
CREATE POLICY "class_members_teacher_manage" ON public.class_members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.classes 
            WHERE id = class_id AND teacher_id = auth.uid()
        )
    );

-- Assignments: Direct policies
-- Teachers can manage their own assignments
CREATE POLICY "assignments_teacher_manage" ON public.assignments
    FOR ALL USING (teacher_id = auth.uid());

-- Students can read assignments for classes they're members of
CREATE POLICY "assignments_student_read" ON public.assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.class_members 
            WHERE class_id = assignments.class_id AND user_id = auth.uid()
        )
    );

-- Submissions: Direct policies
-- Students can manage their own submissions
CREATE POLICY "submissions_student_own" ON public.submissions
    FOR ALL USING (student_id = auth.uid());

-- Teachers can read/update submissions for their assignments
CREATE POLICY "submissions_teacher_access" ON public.submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.assignments 
            WHERE id = assignment_id AND teacher_id = auth.uid()
        )
    );

CREATE POLICY "submissions_teacher_update" ON public.submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.assignments 
            WHERE id = assignment_id AND teacher_id = auth.uid()
        )
    );

-- Tickets: Direct policies
-- Students can manage their own tickets
CREATE POLICY "tickets_student_own" ON public.tickets
    FOR ALL USING (student_id = auth.uid());

-- Teachers can access tickets for their assignments
CREATE POLICY "tickets_teacher_access" ON public.tickets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.submissions s
            INNER JOIN public.assignments a ON s.assignment_id = a.id
            WHERE s.id = submission_id AND a.teacher_id = auth.uid()
        )
    );

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'RLS policies fixed successfully! Infinite recursion issues resolved.' as status,
       'You can now test class creation, grades dispute page, and people page.' as next_steps;
