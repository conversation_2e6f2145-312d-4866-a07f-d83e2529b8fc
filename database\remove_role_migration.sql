-- Migration: Remove Role Column from Users Table
-- This script removes the role-based authentication system and updates related functions
-- Run this script in Supabase SQL Editor

-- Step 1: Drop all existing policies that reference the role column
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Drop all policies that reference user roles
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON ' || r.schemaname || '.' || r.tablename;
    END LOOP;
END $$;

-- Step 2: Drop the role constraint and column from users table
ALTER TABLE public.users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE public.users DROP COLUMN IF EXISTS role;

-- Step 3: Update the handle_new_user function to not use roles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', 'User')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Update notification functions to not reference roles
CREATE OR REPLACE FUNCTION notify_assignment_created()
RETURNS TRIGGER AS $$
DECLARE
  member_record RECORD;
BEGIN
  -- Notify all class members about the new assignment (excluding the teacher who created it)
  FOR member_record IN 
    SELECT cm.user_id 
    FROM public.class_members cm 
    WHERE cm.class_id = NEW.class_id AND cm.user_id != NEW.teacher_id
  LOOP
    PERFORM create_notification(
      member_record.user_id,
      'New Assignment',
      'A new assignment "' || NEW.title || '" has been posted.',
      'assignment',
      NEW.id
    );
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Create new RLS policies without role dependencies
-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.class_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Classes table policies (teachers can manage their classes, members can view)
CREATE POLICY "classes_select_members" ON public.classes
  FOR SELECT USING (
    teacher_id = auth.uid() OR
    id IN (SELECT class_id FROM public.class_members WHERE user_id = auth.uid())
  );

CREATE POLICY "classes_insert_teacher" ON public.classes
  FOR INSERT WITH CHECK (teacher_id = auth.uid());

CREATE POLICY "classes_update_teacher" ON public.classes
  FOR UPDATE USING (teacher_id = auth.uid());

CREATE POLICY "classes_delete_teacher" ON public.classes
  FOR DELETE USING (teacher_id = auth.uid());

-- Class members table policies
CREATE POLICY "class_members_select_related" ON public.class_members
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (SELECT 1 FROM public.classes WHERE id = class_id AND teacher_id = auth.uid())
  );

CREATE POLICY "class_members_insert_teacher_or_self" ON public.class_members
  FOR INSERT WITH CHECK (
    user_id = auth.uid() OR
    EXISTS (SELECT 1 FROM public.classes WHERE id = class_id AND teacher_id = auth.uid())
  );

CREATE POLICY "class_members_delete_teacher_or_self" ON public.class_members
  FOR DELETE USING (
    user_id = auth.uid() OR
    EXISTS (SELECT 1 FROM public.classes WHERE id = class_id AND teacher_id = auth.uid())
  );

-- Assignments table policies
CREATE POLICY "assignments_select_class_members" ON public.assignments
  FOR SELECT USING (
    teacher_id = auth.uid() OR
    class_id IN (SELECT class_id FROM public.class_members WHERE user_id = auth.uid())
  );

CREATE POLICY "assignments_insert_teacher" ON public.assignments
  FOR INSERT WITH CHECK (
    teacher_id = auth.uid() AND
    EXISTS (SELECT 1 FROM public.classes WHERE id = class_id AND teacher_id = auth.uid())
  );

CREATE POLICY "assignments_update_teacher" ON public.assignments
  FOR UPDATE USING (teacher_id = auth.uid());

CREATE POLICY "assignments_delete_teacher" ON public.assignments
  FOR DELETE USING (teacher_id = auth.uid());

-- Submissions table policies
CREATE POLICY "submissions_select_student_or_teacher" ON public.submissions
  FOR SELECT USING (
    student_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.assignments a 
      WHERE a.id = assignment_id AND a.teacher_id = auth.uid()
    )
  );

CREATE POLICY "submissions_insert_student" ON public.submissions
  FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "submissions_update_student_or_teacher" ON public.submissions
  FOR UPDATE USING (
    student_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.assignments a 
      WHERE a.id = assignment_id AND a.teacher_id = auth.uid()
    )
  );

-- Tickets table policies
CREATE POLICY "tickets_select_student_or_teacher" ON public.tickets
  FOR SELECT USING (
    student_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.submissions s
      JOIN public.assignments a ON s.assignment_id = a.id
      WHERE s.id = submission_id AND a.teacher_id = auth.uid()
    )
  );

CREATE POLICY "tickets_insert_student" ON public.tickets
  FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "tickets_update_student_or_teacher" ON public.tickets
  FOR UPDATE USING (
    student_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.submissions s
      JOIN public.assignments a ON s.assignment_id = a.id
      WHERE s.id = submission_id AND a.teacher_id = auth.uid()
    )
  );

-- Notifications table policies
CREATE POLICY "notifications_select_own" ON public.notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "notifications_insert_own" ON public.notifications
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "notifications_update_own" ON public.notifications
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "notifications_delete_own" ON public.notifications
  FOR DELETE USING (user_id = auth.uid());

-- Step 6: Success message
SELECT 'Database schema updated successfully!' as message,
       'Role column removed from users table' as change_1,
       'RLS policies updated for role-free authentication' as change_2,
       'Functions updated to work without roles' as change_3;
