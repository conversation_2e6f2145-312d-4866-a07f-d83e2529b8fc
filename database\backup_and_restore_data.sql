-- <PERSON><PERSON><PERSON><PERSON> AND <PERSON>ESTORE DATA SCRIPT
-- Run this BEFORE the fresh database setup to preserve your data

-- Step 1: Create backup tables to preserve existing data
CREATE TABLE IF NOT EXISTS backup_users AS SELECT * FROM public.users;
CREATE TABLE IF NOT EXISTS backup_classes AS SELECT * FROM public.classes;
CREATE TABLE IF NOT EXISTS backup_class_members AS SELECT * FROM public.class_members;
CREATE TABLE IF NOT EXISTS backup_assignments AS SELECT * FROM public.assignments;
CREATE TABLE IF NOT EXISTS backup_submissions AS SELECT * FROM public.submissions;
CREATE TABLE IF NOT EXISTS backup_tickets AS SELECT * FROM public.tickets;
CREATE TABLE IF NOT EXISTS backup_notifications AS SELECT * FROM public.notifications;

SELECT 'Data backed up successfully!' as status,
       'Run fresh_database_setup.sql next' as next_step;

-- RESTORE SCRIPT (Run this AFTER fresh_database_setup.sql)
-- Uncomment and run the section below after running fresh_database_setup.sql

/*
-- Step 2: Restore data to new tables (run after fresh_database_setup.sql)

-- Restore users (remove role column if it exists)
INSERT INTO public.users (id, email, name, avatar_url, created_at)
SELECT id, email, name, avatar_url, created_at 
FROM backup_users
ON CONFLICT (id) DO NOTHING;

-- Restore classes
INSERT INTO public.classes (id, name, subject, description, class_code, color_scheme, teacher_id, created_at)
SELECT id, name, subject, description, class_code, color_scheme, teacher_id, created_at 
FROM backup_classes
ON CONFLICT (id) DO NOTHING;

-- Restore class_members (ensure role column exists)
INSERT INTO public.class_members (id, class_id, user_id, role, joined_at)
SELECT 
    id, 
    class_id, 
    user_id, 
    COALESCE(role, 'student') as role,
    joined_at 
FROM backup_class_members
ON CONFLICT (class_id, user_id) DO NOTHING;

-- Restore assignments
INSERT INTO public.assignments (id, class_id, title, content, max_marks, due_date, teacher_id, ai_prompt, created_at)
SELECT id, class_id, title, content, max_marks, due_date, teacher_id, ai_prompt, created_at 
FROM backup_assignments
ON CONFLICT (id) DO NOTHING;

-- Restore submissions
INSERT INTO public.submissions (id, assignment_id, student_id, file_url, file_name, ocr_text, grade, feedback, submitted_at, graded_at, graded_by)
SELECT id, assignment_id, student_id, file_url, file_name, ocr_text, grade, feedback, submitted_at, graded_at, graded_by 
FROM backup_submissions
ON CONFLICT (assignment_id, student_id) DO NOTHING;

-- Restore tickets
INSERT INTO public.tickets (id, submission_id, student_id, reason, status, teacher_response, created_at, updated_at)
SELECT id, submission_id, student_id, reason, status, teacher_response, created_at, updated_at 
FROM backup_tickets
ON CONFLICT (id) DO NOTHING;

-- Restore notifications
INSERT INTO public.notifications (id, user_id, title, message, type, related_id, read, created_at)
SELECT id, user_id, title, message, type, related_id, read, created_at 
FROM backup_notifications
ON CONFLICT (id) DO NOTHING;

-- Clean up backup tables
DROP TABLE IF EXISTS backup_users;
DROP TABLE IF EXISTS backup_classes;
DROP TABLE IF EXISTS backup_class_members;
DROP TABLE IF EXISTS backup_assignments;
DROP TABLE IF EXISTS backup_submissions;
DROP TABLE IF EXISTS backup_tickets;
DROP TABLE IF EXISTS backup_notifications;

SELECT 'Data restored successfully!' as status,
       'Fresh database with preserved data ready' as result;
*/
