-- SETU<PERSON> RLS POLICIES AND FUNCTIONS
-- Run this AFTER running clean_database_setup.sql
-- This sets up security policies and automatic functions

-- Step 1: Create RLS policies

-- Users: Users can only access their own data
CREATE POLICY "users_own_data" ON public.users
    FOR ALL USING (auth.uid() = id);

-- Classes: Teachers can manage their classes, members can read
CREATE POLICY "classes_teacher_full_access" ON public.classes
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "classes_member_read_access" ON public.classes
    FOR SELECT USING (
        id IN (
            SELECT class_id FROM public.class_members 
            WHERE user_id = auth.uid()
        )
    );

-- Class Members: Teachers can manage their class members, users can see their own memberships
CREATE POLICY "class_members_teacher_manage" ON public.class_members
    FOR ALL USING (
        class_id IN (
            SELECT id FROM public.classes 
            WHERE teacher_id = auth.uid()
        )
    );

CREATE POLICY "class_members_own_membership" ON public.class_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "class_members_join_classes" ON public.class_members
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Assignments: Teachers can manage, students can read
CREATE POLICY "assignments_teacher_manage" ON public.assignments
    FOR ALL USING (teacher_id = auth.uid());

CREATE POLICY "assignments_student_read" ON public.assignments
    FOR SELECT USING (
        class_id IN (
            SELECT class_id FROM public.class_members 
            WHERE user_id = auth.uid()
        )
    );

-- Submissions: Students manage their own, teachers can read/update for grading
CREATE POLICY "submissions_student_own" ON public.submissions
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "submissions_teacher_read_grade" ON public.submissions
    FOR SELECT USING (
        assignment_id IN (
            SELECT id FROM public.assignments 
            WHERE teacher_id = auth.uid()
        )
    );

CREATE POLICY "submissions_teacher_update_grade" ON public.submissions
    FOR UPDATE USING (
        assignment_id IN (
            SELECT id FROM public.assignments 
            WHERE teacher_id = auth.uid()
        )
    );

-- Tickets: Students manage their own, teachers can access for their assignments
CREATE POLICY "tickets_student_own" ON public.tickets
    FOR ALL USING (student_id = auth.uid());

CREATE POLICY "tickets_teacher_access" ON public.tickets
    FOR ALL USING (
        submission_id IN (
            SELECT s.id FROM public.submissions s
            INNER JOIN public.assignments a ON s.assignment_id = a.id
            WHERE a.teacher_id = auth.uid()
        )
    );

-- Notifications: Users can only access their own notifications
CREATE POLICY "notifications_own_access" ON public.notifications
    FOR ALL USING (user_id = auth.uid());

-- Step 2: Create essential functions

-- Drop existing functions first to avoid conflicts
DROP FUNCTION IF EXISTS public.handle_new_user();
DROP FUNCTION IF EXISTS public.add_teacher_to_class();
DROP FUNCTION IF EXISTS public.create_notification(UUID, TEXT, TEXT, TEXT, UUID);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'User')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically add teacher to their class as a member
CREATE OR REPLACE FUNCTION public.add_teacher_to_class()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.class_members (class_id, user_id, role)
    VALUES (NEW.id, NEW.teacher_id, 'teacher')
    ON CONFLICT (class_id, user_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create notifications
CREATE OR REPLACE FUNCTION public.create_notification(
    target_user_id UUID,
    notification_title TEXT,
    notification_message TEXT,
    notification_type TEXT DEFAULT 'info',
    related_entity_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (user_id, title, message, type, related_id)
    VALUES (target_user_id, notification_title, notification_message, notification_type, related_entity_id)
    RETURNING id INTO notification_id;

    RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create triggers

-- Drop existing triggers first to avoid conflicts
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS add_teacher_to_class_trigger ON public.classes;

-- Trigger to create user profile when auth user is created
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Trigger to add teacher to their class when class is created
CREATE TRIGGER add_teacher_to_class_trigger
    AFTER INSERT ON public.classes
    FOR EACH ROW EXECUTE FUNCTION public.add_teacher_to_class();

-- Success message
SELECT 'RLS policies and functions setup completed!' as status;
